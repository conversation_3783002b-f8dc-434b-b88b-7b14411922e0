# YangYangTravel 微服务技术文档

## 1. 系统架构设计

### 1.1 整体架构图

```mermaid
graph TB
    Client[客户端] --> Gateway[API网关 :8080]
    Gateway --> UserService[用户服务 :8081]
    Gateway --> AdminService[后台管理服务 :8082]

    UserService --> MySQL[(MySQL数据库)]
    AdminService --> MySQL

    UserService --> Eureka[服务注册中心 :8761]
    AdminService --> Eureka
    Gateway --> Eureka

    UserService --> Config[配置中心 :8888]
    AdminService --> Config
    Gateway --> Config
```

### 1.2 技术选型说明

| 组件 | 技术选型 | 版本 | 说明 |
|------|----------|------|------|
| 基础框架 | Spring Boot | 3.2.0 | 微服务基础框架 |
| 微服务框架 | Spring Cloud | 2023.0.0 | 微服务治理框架 |
| 服务注册发现 | Eureka | 4.1.0 | 服务注册与发现 |
| 配置中心 | Spring Cloud Config | 4.1.0 | 集中化配置管理 |
| API网关 | Spring Cloud Gateway | 4.1.0 | 统一入口网关 |
| 数据库 | MySQL | 8.0+ | 关系型数据库 |
| ORM框架 | MyBatis Plus | 3.5.5 | 数据访问层框架 |
| API文档 | Swagger/OpenAPI | 3.0 | API文档生成 |
| 构建工具 | Maven | 3.9+ | 项目构建管理 |

### 1.3 服务端口分配

| 服务名称 | 端口 | 说明 |
|----------|------|------|
| eureka-server | 8761 | 服务注册中心 |
| config-server | 8888 | 配置中心 |
| gateway-service | 8080 | API网关 |
| user-service | 8081 | 用户服务 |
| admin-service | 8082 | 后台管理服务 |

## 2. 数据库设计

### 2.1 数据库连接配置

```yaml
spring:
  datasource:
    url: **************************************************************************************************************************************************
    username: yangyangchuyou
    password: Yangyang123@qwe
    driver-class-name: com.mysql.cj.jdbc.Driver
```

### 2.2 表结构设计

#### user_info 用户信息表

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | BIGINT | - | NOT NULL | AUTO_INCREMENT | 主键ID |
| username | VARCHAR | 50 | NOT NULL | - | 用户名（唯一） |
| nickname | VARCHAR | 50 | NULL | - | 昵称 |
| phone | VARCHAR | 20 | NULL | - | 手机号 |
| email | VARCHAR | 100 | NULL | - | 邮箱 |
| avatar_url | VARCHAR | 255 | NULL | - | 头像URL |
| gender | TINYINT | - | NULL | 0 | 性别：0-未知，1-男，2-女 |
| create_time | DATETIME | - | NULL | CURRENT_TIMESTAMP | 创建时间 |
| update_time | DATETIME | - | NULL | CURRENT_TIMESTAMP ON UPDATE | 更新时间 |
| status | TINYINT | - | NULL | 1 | 状态：0-禁用，1-启用 |

### 2.3 索引设计

```sql
-- 主键索引
PRIMARY KEY (`id`)

-- 唯一索引
UNIQUE KEY `uk_username` (`username`)

-- 普通索引
KEY `idx_status` (`status`),
KEY `idx_create_time` (`create_time`)
```

## 3. API接口设计

### 3.1 统一响应格式

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-01-01T12:00:00"
}
```

### 3.2 错误码定义

| 错误码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

### 3.3 用户服务API

#### 3.3.1 查询用户列表
- **接口地址**: GET /api/users
- **请求参数**:
  ```json
  {
    "page": 1,
    "size": 10,
    "username": "可选，用户名模糊查询",
    "status": "可选，用户状态"
  }
  ```
- **响应示例**:
  ```json
  {
    "code": 200,
    "message": "查询成功",
    "data": {
      "records": [
        {
          "id": 1,
          "username": "testuser",
          "nickname": "测试用户",
          "phone": "13800138000",
          "email": "<EMAIL>",
          "avatarUrl": "http://example.com/avatar.jpg",
          "gender": 1,
          "createTime": "2024-01-01T12:00:00",
          "updateTime": "2024-01-01T12:00:00",
          "status": 1
        }
      ],
      "total": 100,
      "current": 1,
      "size": 10
    }
  }
  ```

#### 3.3.2 根据ID查询用户
- **接口地址**: GET /api/users/{id}
- **路径参数**: id - 用户ID
- **响应示例**:
  ```json
  {
    "code": 200,
    "message": "查询成功",
    "data": {
      "id": 1,
      "username": "testuser",
      "nickname": "测试用户",
      "phone": "13800138000",
      "email": "<EMAIL>",
      "avatarUrl": "http://example.com/avatar.jpg",
      "gender": 1,
      "createTime": "2024-01-01T12:00:00",
      "updateTime": "2024-01-01T12:00:00",
      "status": 1
    }
  }
  ```

#### 3.3.3 新增用户
- **接口地址**: POST /api/users
- **请求体**:
  ```json
  {
    "username": "newuser",
    "nickname": "新用户",
    "phone": "13800138001",
    "email": "<EMAIL>",
    "avatarUrl": "http://example.com/avatar.jpg",
    "gender": 1
  }
  ```

#### 3.3.4 修改用户信息
- **接口地址**: PUT /api/users/{id}
- **路径参数**: id - 用户ID
- **请求体**: 同新增用户

#### 3.3.5 删除用户
- **接口地址**: DELETE /api/users/{id}
- **路径参数**: id - 用户ID

### 3.4 后台管理服务API

#### 3.4.1 管理员登录
- **接口地址**: POST /admin/login
- **请求体**:
  ```json
  {
    "username": "admin",
    "password": "1234@qwer"
  }
  ```
- **响应示例**:
  ```json
  {
    "code": 200,
    "message": "登录成功",
    "data": {
      "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "username": "admin",
      "loginTime": "2024-01-01T12:00:00"
    }
  }
  ```

#### 3.4.2 用户管理接口
- 用户列表查询（支持分页）
- 用户信息编辑
- 用户删除
- 用户状态管理（启用/禁用）

## 4. 配置管理

### 4.1 配置中心结构

```
config-repo/
├── application.yml          # 公共配置
├── eureka-server.yml       # 服务注册中心配置
├── gateway-service.yml     # 网关服务配置
├── user-service.yml        # 用户服务配置
└── admin-service.yml       # 后台管理服务配置
```

### 4.2 公共配置示例

```yaml
# application.yml
spring:
  datasource:
    url: **************************************************************************************************************************************************
    username: yangyangchuyou
    password: Yangyang123@qwe
    driver-class-name: com.mysql.cj.jdbc.Driver

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

logging:
  level:
    com.yangyang.travel: debug
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n'
```

## 5. 部署方案

### 5.1 本地开发环境

1. 确保Java 17、Maven 3.9+、MySQL 8.0已安装
2. 按顺序启动各个服务：
   - eureka-server (8761)
   - config-server (8888)
   - gateway-service (8080)
   - user-service (8081)
   - admin-service (8082)

### 5.2 Docker容器化部署

#### 5.2.1 Dockerfile示例

```dockerfile
FROM openjdk:17-jre-slim

VOLUME /tmp

COPY target/*.jar app.jar

EXPOSE 8080

ENTRYPOINT ["java", "-jar", "/app.jar"]
```

#### 5.2.2 docker-compose.yml

```yaml
version: '3.8'

services:
  eureka-server:
    build: ./eureka-server
    ports:
      - "8761:8761"
    environment:
      - SPRING_PROFILES_ACTIVE=docker

  config-server:
    build: ./config-server
    ports:
      - "8888:8888"
    depends_on:
      - eureka-server
    environment:
      - SPRING_PROFILES_ACTIVE=docker

  gateway-service:
    build: ./gateway-service
    ports:
      - "8080:8080"
    depends_on:
      - eureka-server
      - config-server
    environment:
      - SPRING_PROFILES_ACTIVE=docker

  user-service:
    build: ./user-service
    ports:
      - "8081:8081"
    depends_on:
      - eureka-server
      - config-server
    environment:
      - SPRING_PROFILES_ACTIVE=docker

  admin-service:
    build: ./admin-service
    ports:
      - "8082:8082"
    depends_on:
      - eureka-server
      - config-server
    environment:
      - SPRING_PROFILES_ACTIVE=docker
```

## 6. 监控和日志

### 6.1 健康检查

每个服务都集成了Spring Boot Actuator，提供健康检查端点：
- http://localhost:8081/actuator/health
- http://localhost:8082/actuator/health

### 6.2 日志配置

使用Logback作为日志框架，支持：
- 控制台输出
- 文件输出
- 日志级别配置
- 日志滚动策略

## 7. 安全考虑

### 7.1 数据库安全
- 使用专用数据库用户
- 限制数据库访问权限
- 定期备份数据

### 7.2 API安全
- 管理员接口需要身份验证
- 敏感信息加密存储
- 输入参数验证

### 7.3 网络安全
- 使用HTTPS协议
- 配置防火墙规则
- 定期更新依赖版本

## 8. 性能优化

### 8.1 数据库优化
- 合理设计索引
- 使用连接池
- 查询优化

### 8.2 缓存策略
- Redis缓存热点数据
- 本地缓存配置
- 缓存更新策略

### 8.3 负载均衡
- 服务多实例部署
- 负载均衡算法配置
- 健康检查机制

## 9. 测试策略

### 9.1 单元测试
- 使用JUnit 5
- Mockito模拟依赖
- 测试覆盖率要求80%+

### 9.2 集成测试
- Spring Boot Test
- TestContainers数据库测试
- API接口测试

### 9.3 性能测试
- JMeter压力测试
- 响应时间监控
- 并发性能测试

## 10. 运维指南

### 10.1 服务启动顺序
1. 数据库服务
2. 服务注册中心
3. 配置中心
4. 业务服务
5. API网关

### 10.2 故障排查
- 查看服务日志
- 检查服务注册状态
- 验证配置信息
- 数据库连接检查

### 10.3 版本升级
- 灰度发布策略
- 数据库迁移脚本
- 回滚方案准备
