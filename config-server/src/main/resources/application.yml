server:
  port: 8888

spring:
  application:
    name: config-server
  cloud:
    config:
      server:
        git:
          # 配置文件存储的本地路径
          uri: file:///${user.dir}/config-repo
          # 默认分支
          default-label: main
          # 搜索路径
          search-paths: /
        # 本地配置文件模式（开发环境使用）
        native:
          search-locations: classpath:/config/

# Eureka 客户端配置
eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
  instance:
    prefer-ip-address: true
    instance-id: ${spring.application.name}:${server.port}

# 日志配置
logging:
  level:
    org.springframework.cloud.config: debug
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n'

# 监控端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,refresh,env
  endpoint:
    health:
      show-details: always