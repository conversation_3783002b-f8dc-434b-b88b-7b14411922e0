-- YangYangTravel 微服务项目数据库初始化脚本
-- 数据库连接信息：
-- 服务器：rm-uf6gb8bsu7t855li5qo.mysql.rds.aliyuncs.com:3306
-- 数据库：yangyangchuyou
-- 用户名：yangyangchuyou
-- 密码：Yangyang123@qwe

-- 使用数据库
USE yangyangchuyou;

-- 删除已存在的表（如果存在）
DROP TABLE IF EXISTS admin_user;
DROP TABLE IF EXISTS user_info;

-- 创建用户信息表
CREATE TABLE user_info (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    nickname VARCHAR(50) COMMENT '昵称',
    phone VARCHAR(20) COMMENT '手机号',
    email VARCHAR(100) COMMENT '邮箱',
    avatar_url VARCHAR(255) COMMENT '头像URL',
    gender TINYINT DEFAULT 0 COMMENT '性别：0-未知，1-男，2-女',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户信息表';

-- 创建管理员表
CREATE TABLE admin_user (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '管理员用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码（BCrypt加密）',
    real_name VARCHAR(50) COMMENT '真实姓名',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    last_login_time DATETIME COMMENT '最后登录时间',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员表';

-- 创建索引
-- 用户信息表索引
CREATE UNIQUE INDEX uk_user_username ON user_info(username);
CREATE INDEX idx_user_status ON user_info(status);
CREATE INDEX idx_user_create_time ON user_info(create_time);
CREATE INDEX idx_user_phone ON user_info(phone);
CREATE INDEX idx_user_email ON user_info(email);

-- 管理员表索引
CREATE UNIQUE INDEX uk_admin_username ON admin_user(username);
CREATE INDEX idx_admin_status ON admin_user(status);

-- 插入测试用户数据
INSERT INTO user_info (username, nickname, phone, email, avatar_url, gender, status) VALUES
('testuser1', '测试用户1', '13800138001', '<EMAIL>', 'https://example.com/avatar1.jpg', 1, 1),
('testuser2', '测试用户2', '13800138002', '<EMAIL>', 'https://example.com/avatar2.jpg', 2, 1),
('testuser3', '测试用户3', '13800138003', '<EMAIL>', 'https://example.com/avatar3.jpg', 0, 1),
('zhangsan', '张三', '13800138004', '<EMAIL>', 'https://example.com/avatar4.jpg', 1, 1),
('lisi', '李四', '13800138005', '<EMAIL>', 'https://example.com/avatar5.jpg', 2, 1),
('wangwu', '王五', '13800138006', '<EMAIL>', 'https://example.com/avatar6.jpg', 1, 0),
('zhaoliu', '赵六', '13800138007', '<EMAIL>', 'https://example.com/avatar7.jpg', 2, 1),
('qianqi', '钱七', '13800138008', '<EMAIL>', 'https://example.com/avatar8.jpg', 1, 1),
('sunba', '孙八', '13800138009', '<EMAIL>', 'https://example.com/avatar9.jpg', 2, 1),
('zhoujiu', '周九', '13800138010', '<EMAIL>', 'https://example.com/avatar10.jpg', 0, 1);

-- 插入管理员数据
-- 密码：1234@qwer (BCrypt加密后的值)
INSERT INTO admin_user (username, password, real_name, status) VALUES
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDa', '系统管理员', 1),
('manager', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDa', '业务管理员', 1);

-- 查看创建结果
SELECT '用户信息表创建完成，当前记录数：' as message, COUNT(*) as count FROM user_info;
SELECT '管理员表创建完成，当前记录数：' as message, COUNT(*) as count FROM admin_user;

-- 显示表结构
SHOW CREATE TABLE user_info;
SHOW CREATE TABLE admin_user;

-- 显示索引信息
SHOW INDEX FROM user_info;
SHOW INDEX FROM admin_user;

-- 测试查询
SELECT
    id, username, nickname, phone, email, gender,
    create_time, update_time, status
FROM user_info
WHERE status = 1
ORDER BY create_time DESC
LIMIT 5;

SELECT
    id, username, real_name, create_time, status
FROM admin_user
WHERE status = 1;

-- 数据库初始化完成提示
SELECT '=== YangYangTravel 数据库初始化完成 ===' as message;
SELECT '用户服务可以正常访问用户数据' as user_service_status;
SELECT '管理员服务可以正常进行身份验证' as admin_service_status;
SELECT '默认管理员账号：admin，密码：1234@qwer' as admin_account;