# YangYangTravel 微服务项目

## 项目简介

YangYangTravel 是一个基于 Spring Cloud 微服务架构的旅游管理系统，提供用户管理和后台管理功能。

## 技术栈

- **编程语言**: Java 17
- **框架**: Spring Cloud 2023.0.0 (最新稳定版本)
- **数据库**: MySQL 8.0
- **ORM框架**: MyBatis Plus 3.5.5
- **API文档**: Swagger/OpenAPI 3.0
- **构建工具**: Maven 3.9+
- **服务注册**: Eureka Server
- **配置中心**: Spring Cloud Config
- **API网关**: Spring Cloud Gateway
- **容器化**: Docker & Docker Compose

## 项目架构

```
YangYangTravel/
├── eureka-server/          # 服务注册中心 (端口: 8761)
├── config-server/          # 配置中心 (端口: 8888)
├── gateway-service/        # API网关 (端口: 8080)
├── user-service/          # 用户服务 (端口: 8081)
├── admin-service/         # 后台管理服务 (端口: 8082)
├── common/               # 公共模块
├── config-repo/          # 配置文件仓库
├── docker/              # Docker配置文件
└── docs/               # 项目文档
```

## 服务模块说明

### 1. 服务注册中心 (eureka-server)
- **端口**: 8761
- **功能**: 服务注册与发现
- **访问地址**: http://localhost:8761

### 2. 配置中心 (config-server)
- **端口**: 8888
- **功能**: 集中化配置管理
- **访问地址**: http://localhost:8888

### 3. API网关 (gateway-service)
- **端口**: 8080
- **功能**: 统一入口、路由转发、负载均衡
- **访问地址**: http://localhost:8080

### 4. 用户服务 (user-service)
- **端口**: 8081
- **功能**: 用户信息CRUD操作
- **主要API**:
  - GET /api/users - 查询用户列表
  - GET /api/users/{id} - 根据ID查询用户
  - POST /api/users - 新增用户
  - PUT /api/users/{id} - 修改用户信息
  - DELETE /api/users/{id} - 删除用户

### 5. 后台管理服务 (admin-service)
- **端口**: 8082
- **功能**: 管理员后台管理系统
- **主要功能**:
  - 管理员登录验证
  - 用户信息列表展示（分页）
  - 用户信息编辑和删除
  - 用户状态管理（启用/禁用）

## 数据库配置

### MySQL连接信息
- **服务器地址**: rm-uf6gb8bsu7t855li5qo.mysql.rds.aliyuncs.com:3306
- **数据库名称**: yangyangchuyou
- **用户名**: yangyangchuyou
- **密码**: Yangyang123@qwe

### 数据库表结构

#### user_info 表
```sql
CREATE TABLE user_info (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    nickname VARCHAR(50) COMMENT '昵称',
    phone VARCHAR(20) COMMENT '手机号',
    email VARCHAR(100) COMMENT '邮箱',
    avatar_url VARCHAR(255) COMMENT '头像URL',
    gender TINYINT DEFAULT 0 COMMENT '性别：0-未知，1-男，2-女',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户信息表';
```

## 管理员账号

- **用户名**: admin
- **密码**: 1234@qwer

## 快速开始

### 环境要求
- JDK 17+
- Maven 3.9+
- MySQL 8.0+
- Docker (可选)

### 本地开发启动顺序

1. **启动服务注册中心**
   ```bash
   cd eureka-server
   mvn spring-boot:run
   ```

2. **启动配置中心**
   ```bash
   cd config-server
   mvn spring-boot:run
   ```

3. **启动API网关**
   ```bash
   cd gateway-service
   mvn spring-boot:run
   ```

4. **启动用户服务**
   ```bash
   cd user-service
   mvn spring-boot:run
   ```

5. **启动后台管理服务**
   ```bash
   cd admin-service
   mvn spring-boot:run
   ```

### Docker部署

```bash
# 构建所有服务镜像
mvn clean package -DskipTests

# 启动所有服务
docker-compose up -d
```

## API文档

启动服务后，可通过以下地址访问API文档：

- **用户服务API**: http://localhost:8081/swagger-ui.html
- **后台管理API**: http://localhost:8082/swagger-ui.html
- **网关聚合API**: http://localhost:8080/swagger-ui.html

## 测试

```bash
# 运行所有测试
mvn test

# 运行特定模块测试
cd user-service
mvn test
```

## 项目特性

- ✅ 微服务架构设计
- ✅ 服务注册与发现
- ✅ 配置中心管理
- ✅ API网关统一入口
- ✅ 统一异常处理
- ✅ 统一返回结果封装
- ✅ Swagger API文档
- ✅ MyBatis Plus集成
- ✅ 单元测试覆盖
- ✅ Docker容器化部署

## 开发规范

### 代码规范
- 遵循阿里巴巴Java开发手册
- 使用统一的代码格式化配置
- 必要的注释和文档

### API设计规范
- RESTful API设计
- 统一的响应格式
- 完整的错误码定义

### 数据库规范
- 统一的命名规范
- 必要的索引设计
- 数据库版本管理

## 联系方式

如有问题，请联系项目维护者。
