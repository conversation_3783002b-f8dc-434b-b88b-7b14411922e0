# YangYangTravel 微服务项目实施指南

## 项目状态

✅ **已完成的模块：**
1. 项目架构设计和文档生成
2. 父项目POM配置
3. 服务注册中心模块 (Eureka Server)
4. 配置中心模块 (Config Server)
5. 基础目录结构创建

## 当前项目结构

```
YangYangTravel/
├── README.md                    # 项目说明文档
├── pom.xml                     # 父项目POM配置
├── docs/                       # 项目文档
│   └── 技术文档.md
├── config-repo/                # 配置文件仓库
│   └── application.yml         # 公共配置
├── eureka-server/              # 服务注册中心 ✅
│   ├── pom.xml
│   └── src/main/
│       ├── java/com/yangyang/travel/eureka/
│       │   └── EurekaServerApplication.java
│       └── resources/
│           └── application.yml
├── config-server/              # 配置中心 ✅
│   ├── pom.xml
│   └── src/main/
│       ├── java/com/yangyang/travel/config/
│       │   └── ConfigServerApplication.java
│       └── resources/
│           └── application.yml
├── common/                     # 公共模块 🔄
│   ├── pom.xml
│   └── src/main/java/com/yangyang/travel/common/
├── gateway-service/            # API网关 🔄
├── user-service/              # 用户服务 ⏳
└── admin-service/             # 后台管理服务 ⏳
```

## 数据库初始化脚本

```sql
-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS yangyangchuyou DEFAULT CHARSET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE yangyangchuyou;

-- 创建用户信息表
CREATE TABLE user_info (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    nickname VARCHAR(50) COMMENT '昵称',
    phone VARCHAR(20) COMMENT '手机号',
    email VARCHAR(100) COMMENT '邮箱',
    avatar_url VARCHAR(255) COMMENT '头像URL',
    gender TINYINT DEFAULT 0 COMMENT '性别：0-未知，1-男，2-女',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户信息表';

-- 创建索引
CREATE UNIQUE INDEX uk_username ON user_info(username);
CREATE INDEX idx_status ON user_info(status);
CREATE INDEX idx_create_time ON user_info(create_time);

-- 插入测试数据
INSERT INTO user_info (username, nickname, phone, email, gender) VALUES
('testuser1', '测试用户1', '13800138001', '<EMAIL>', 1),
('testuser2', '测试用户2', '13800138002', '<EMAIL>', 2),
('testuser3', '测试用户3', '13800138003', '<EMAIL>', 0);

-- 创建管理员表（用于后台管理）
CREATE TABLE admin_user (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '管理员用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';

-- 插入默认管理员账号（密码：1234@qwer，需要BCrypt加密）
INSERT INTO admin_user (username, password) VALUES
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDa');
```

## 启动顺序

1. **启动服务注册中心**
   ```bash
   cd eureka-server
   mvn spring-boot:run
   ```

2. **启动配置中心**
   ```bash
   cd config-server
   mvn spring-boot:run
   ```

3. **启动API网关**
   ```bash
   cd gateway-service
   mvn spring-boot:run
   ```

4. **启动用户服务**
   ```bash
   cd user-service
   mvn spring-boot:run
   ```

5. **启动后台管理服务**
   ```bash
   cd admin-service
   mvn spring-boot:run
   ```

## 访问地址

- **Eureka控制台**: http://localhost:8761
- **配置中心**: http://localhost:8888
- **API网关**: http://localhost:8080
- **用户服务**: http://localhost:8081
- **后台管理**: http://localhost:8082
- **Swagger文档**: http://localhost:8080/swagger-ui.html

## 核心API接口

### 用户服务API
- GET /api/users - 查询用户列表
- GET /api/users/{id} - 根据ID查询用户
- POST /api/users - 新增用户
- PUT /api/users/{id} - 修改用户信息
- DELETE /api/users/{id} - 删除用户

### 后台管理API
- POST /admin/login - 管理员登录
- GET /admin/users - 用户管理列表
- PUT /admin/users/{id}/status - 修改用户状态

## 测试用例

### 1. 测试用户服务
```bash
# 查询用户列表
curl -X GET "http://localhost:8080/api/users?page=1&size=10"

# 根据ID查询用户
curl -X GET "http://localhost:8080/api/users/1"

# 新增用户
curl -X POST "http://localhost:8080/api/users" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "newuser",
    "nickname": "新用户",
    "phone": "13800138888",
    "email": "<EMAIL>",
    "gender": 1
  }'
```

### 2. 测试管理员登录
```bash
curl -X POST "http://localhost:8080/admin/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "1234@qwer"
  }'
```

## 下一步完善工作

1. ✅ 完成基础架构搭建
2. 🔄 完善各服务模块实现
3. ⏳ 添加统一异常处理
4. ⏳ 集成完整Swagger文档
5. ⏳ 编写单元测试
6. ⏳ 创建Docker配置
7. ⏳ 完善监控和日志

## 重要提醒

1. **数据库连接**: 确保MySQL服务正常，数据库连接信息正确
2. **启动顺序**: 严格按照启动顺序启动各个服务
3. **端口占用**: 确保8761、8888、8080、8081、8082端口未被占用
4. **Java版本**: 确保使用Java 17
5. **Maven版本**: 确保使用Maven 3.9+

## 故障排查

1. **服务无法启动**: 检查端口占用和Java版本
2. **服务注册失败**: 检查Eureka Server是否正常启动
3. **配置加载失败**: 检查Config Server和配置文件路径
4. **数据库连接失败**: 检查数据库连接信息和网络连通性

项目已经搭建了完整的微服务基础架构，可以按照上述指南继续完善各个模块的具体实现。