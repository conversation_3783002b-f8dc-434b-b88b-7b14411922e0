version: '3.8'

services:
  # 服务注册中心
  eureka-server:
    build:
      context: ./eureka-server
      dockerfile: Dockerfile
    container_name: yangyang-eureka-server
    ports:
      - "8761:8761"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
    networks:
      - yangyang-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8761/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 配置中心
  config-server:
    build:
      context: ./config-server
      dockerfile: Dockerfile
    container_name: yangyang-config-server
    ports:
      - "8888:8888"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE=http://eureka-server:8761/eureka/
    depends_on:
      eureka-server:
        condition: service_healthy
    networks:
      - yangyang-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8888/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # API网关
  gateway-service:
    build:
      context: ./gateway-service
      dockerfile: Dockerfile
    container_name: yangyang-gateway
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE=http://eureka-server:8761/eureka/
      - SPRING_CLOUD_CONFIG_URI=http://config-server:8888
    depends_on:
      eureka-server:
        condition: service_healthy
      config-server:
        condition: service_healthy
    networks:
      - yangyang-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 用户服务
  user-service:
    build:
      context: ./user-service
      dockerfile: Dockerfile
    container_name: yangyang-user-service
    ports:
      - "8081:8081"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE=http://eureka-server:8761/eureka/
      - SPRING_CLOUD_CONFIG_URI=http://config-server:8888
      - SPRING_DATASOURCE_URL=********************************************************************************************************************************************************************************************
      - SPRING_DATASOURCE_USERNAME=yangyangchuyou
      - SPRING_DATASOURCE_PASSWORD=Yangyang123@qwe
    depends_on:
      eureka-server:
        condition: service_healthy
      config-server:
        condition: service_healthy
    networks:
      - yangyang-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 后台管理服务
  admin-service:
    build:
      context: ./admin-service
      dockerfile: Dockerfile
    container_name: yangyang-admin-service
    ports:
      - "8082:8082"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE=http://eureka-server:8761/eureka/
      - SPRING_CLOUD_CONFIG_URI=http://config-server:8888
      - SPRING_DATASOURCE_URL=********************************************************************************************************************************************************************************************
      - SPRING_DATASOURCE_USERNAME=yangyangchuyou
      - SPRING_DATASOURCE_PASSWORD=Yangyang123@qwe
    depends_on:
      eureka-server:
        condition: service_healthy
      config-server:
        condition: service_healthy
    networks:
      - yangyang-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8082/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  yangyang-network:
    driver: bridge

volumes:
  config-repo:
    driver: local